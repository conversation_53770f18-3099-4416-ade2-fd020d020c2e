// @use "ag-grid-community/styles/ag-grid.css";
// @use "ag-grid-community/styles/ag-theme-alpine.css";
//Import primeicons
@use "primeicons/primeicons.css";

.action-icon {
    font-size: 1.25rem !important;
    color: #517EBC;
    cursor: pointer;
}

.tps-table-action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background: #f8f9fd !important;
    border: 1px solid #E7EAF0;
    border-radius: 0px;
    padding: 4px 16px 4px 6px;
    min-height: 40px !important;
    // height: 48px !important;
}

// .search-text {
//     width: 300px !important;
// }

.no-data_wrapper {
    width: 100%;
    background: white;
    // box-shadow: 0px 4px 4px rgb(123 134 153 / 30%);
    border-radius: 0px 0px 11px 11px;
    border: 1px solid #E7EAF0;
    border-top: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-bottom: 2rem;
}

.no-data-img {
    width: 200px;
    height: 200px;
}

.no-data-ava {

    margin-top: 25px;
    font-weight: 400;
    font-size: 24px;
    line-height: 30px;
    color: #0F1925;
}

.no-data-label {
    &span {
        font-weight: 400;
        font-size: 18px;
        line-height: 23px;
        color: #7B8699
    }
}

.ag-theme-alpine .ag-column-first {
    padding-left: 16px !important;
}

.ag-body-viewport {
    overflow-x: auto;
    /* Ensure horizontal scrolling is allowed */
}