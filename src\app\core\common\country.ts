import { SelectItem } from 'primeng/api';

export class CountryList {
  public countries: SelectItem[];
  public selectedCtry: string = "";
  private data

  constructor(ctry: string=''){
    this.countries = [];
    this.countries.push({label:'Bangladesh', value:'Bangladesh'});
    this.countries.push({label:'China', value:'China'});
    this.countries.push({label:'Hong Kong', value:'Hong Kong'});
    this.countries.push({label:'India', value:'India'});
    this.countries.push({label:'Indonesia', value:'Indonesia'});
    this.countries.push({label:'Italy', value:'Italy'});
    this.countries.push({label:'Mauritius', value:'Mauritius'});
    this.countries.push({label:'Nepal', value:'Nepal'});
    this.countries.push({label:'Sri Lanka', value:'Sri Lanka'});
    this.countries.push({label:'Thailand', value:'Thailand'});
    this.countries.push({label:'UAE', value:'UAE'});
    this.countries.push({label:'United Kingdom', value:'United Kingdom'});
    this.countries.push({label:'Vietnam', value:'Vietnam'});  
    this.countries.push({label:'Other', value:'Other'});    
    this.selectedCtry = ctry;
  }

  setCountry(ctry: string){
    this.selectedCtry = ctry;
  }

  getSelectedValue(key: string, attrib: string, def: string = '*') {
    let _value = '';
    if (this.data == null || this.data == undefined) return _value;
    this.data.forEach(element => {
      if (element[key] == this.selectedCtry) {
        _value = element[attrib];
      }
    });
    if (_value == null || _value == undefined || _value == '') _value = def;
    return _value;
  }
}
