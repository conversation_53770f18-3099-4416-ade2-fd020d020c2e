import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Vendor } from 'app/core/models/vendor.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  actionType,
  buttonActions,
  CommonService,
  ICON_BUTTON_SEVERITY,
  ICON_PRIMENG_LIST,
  InvokeService,
  TABLE_ACTION_TYPES,
  toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';
import { CreateEditVendorComponent } from './create-edit-vendor/create-edit-vendor.component';

@Component({
  selector: 'tps-vendor',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './vendor.component.html'
})
export class VendorComponent implements OnInit, OnD<PERSON>roy {
  hdr: string = 'Vendors';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  vendors: Vendor[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Vendor',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.VENDOR_CREATE,
        command: () => {
          this.onOpenVendorDialog('Create Vendor', new Vendor(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getVendors();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      {
        headerName: "Username",
        field: "userName",
        sortable: true,
      },
      {
        headerName: "Full Name",
        field: "fullName",
        sortable: true,
      },
      {
        headerName: "City",
        field: "city",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenVendorDialog('View Vendor Details', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.VENDOR_UPDATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenVendorDialog('Edit Vendor Details', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (this.ACL_LIST?.VENDOR_DELETE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DELETE,
        icon: ICON_PRIMENG_LIST.PI_TRASH,
        title: "Delete",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.deleteVendor(row)
      });
    }

    return iconsList;
  }

  private getVendors(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.vendors.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.prepareVendorData(this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareVendorData(data: any[]): void {
    this.vendors = data.map(item => ({
      createdTimeText: this._commonService.dateTimeFormat(item.createdTime),
      ...item
    }));
  }

  private deleteVendor(row: Vendor): void {
    this._commonService.confirm('Are you sure you want to delete this vendor?', row.firstName)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.vendors.delete, row.uuid)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Vendor deleted successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  private onOpenVendorDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditVendorComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
