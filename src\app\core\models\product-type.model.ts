export interface iProductType {
  code: string;
  id?: number;
  product?: string;
  description?: string;
  createdOn?: number;
  modifiedOn?: number;
  uuid?: string;
  createdBy?: string;
  modifiedBy?: string;
  createdDateText?: string;
  name?:string;
};

export class ProductType implements iProductType {
  public code: string;
  public id?: number;
  public product?: string;
  public description?: string;
  public createdOn?: number;
  public modifiedOn?: number;
  public uuid?: string;
  public createdBy?: string;
  public modifiedBy?: string;
  public createdDateText?: string;
  public name?:string;
};