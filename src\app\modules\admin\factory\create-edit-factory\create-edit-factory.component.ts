import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { Factory } from 'app/core/models/factory.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { distinctUntilChanged, ReplaySubject, takeUntil } from 'rxjs';

import { FACTORY_FORM_MODEL } from './factory-form.model';

@Component({
  selector: 'tpd-create-edit-factory',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-factory.component.html',
  styleUrl: './create-edit-factory.component.scss',
})
export class CreateEditFactoryComponent implements OnInit, OnDestroy {
  factoryForm: FormGroup;
  public hdr: string = 'Create Factory';
  public fields: any[] = [];
  public rec: Factory = new Factory();
  isMobile: boolean = false;
  afterAuditScore: boolean = false;
  afterAuditDate: boolean = false;
  auditRating: any[] = [{ label: "Fail", value: "Fail" , id:0}, { label: "Average", value: "Average", id:1 }, { label: "Good", value: "Good",id:2 }, { label: "Excellent", value: "Excellent", id:3 }];
  selectedAuditRating: string;
  complianceAuditDt: Date;

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;

  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  constructor(
    private _formFactoryService: FormFactoryService,
    private _dialogRef: DynamicDialogRef,
    private _dialogConfig: DynamicDialogConfig,
    private _commonService: CommonService,
    private _invokeService: InvokeService
  ) { }

  ngOnInit(): void {
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });
    this.complianceAuditDt = new Date();
    this.status = this._dialogConfig.data?.status;
    this.buildForm();
    this.configureForm();
  }

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(FACTORY_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(FACTORY_FORM_MODEL);
    this.factoryForm = new FormGroup(formGroupFields);
    this.fields = this._formFactoryService.setOptionsToDropDown(this.fields, 'auditRating', this.auditRating);
  }

  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        this.factoryForm.controls['validUpto'].disable();
        this.factoryForm.controls['auditRating'].disable();
        this.factoryForm.controls['factoryAuditDate'].disable();
        this.factoryForm.get('factoryAuditScore').setValue(null)
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Factory";
        this.setDataToForm();
        this.factoryForm.controls['code'].disable();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View Factory";
        this.setDataToForm();
        this.factoryForm.disable();
        break;
      default:
        break;
    }
    this.onFormChanges();
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    for (var i = 0; i < this.auditRating.length; i++) {
      if (this.rec.riskLevel == this.auditRating[i].id) {
         this.rec.auditRating = this.auditRating[i].value;
        break;
      }
    }
    this.complianceAuditDt = new Date(this.rec.cvalidUpto);
    this.factoryForm = this._formFactoryService.setFormControlsValues(this.factoryForm, this.rec, this.fields);
  }

  public onSubmit() {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.factoryForm, this.rec, this.fields);
    this.rec.cvalidUpto = this.complianceAuditDt.getTime();
    for (var i = 0; i < this.auditRating.length; i++) {
      if (this.rec.auditRating == this.auditRating[i].value) {
        this.rec.riskLevel =  this.auditRating[i].id;
        break;
      }
    }

    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update this factory?`, this.rec.name)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.factory.update, this.rec.id, Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Factory updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the factory details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    } else {
      this._commonService.confirm(`Are you sure you want to create a new factory?`, this.rec.name)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.factory.addFactory, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New factory created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the factory details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    }
  }

  public onFormChanges(): void {
    this.factoryForm.get('factoryAuditDate').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          console.log("factoryAuditDate", response);
          this.onAuditDateChange()
        }
      });
    this.factoryForm.get('factoryAuditScore').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          console.log("factoryAuditScore", response);
          const val = this.factoryForm.get('factoryAuditScore').value;
          this.factoryForm.controls['validUpto'].enable();
          this.factoryForm.controls['auditRating'].enable();
          this.factoryForm.controls['factoryAuditDate'].enable();
          this.calAuditRating(val);
        }
      });
  }

  public onAuditDateChange(): void {
    let date = this.factoryForm.get('factoryAuditDate').value;
    if (this.selectedAuditRating && date) {
      let newdate = new Date;
      let month;
      let day;
      let year;
      if (this.selectedAuditRating == 'Excellent') {
        newdate = new Date(new Date(date).setMonth(date.getMonth() + 18));
        month = newdate.getMonth();
        day = newdate.getDate();
        year = newdate.getFullYear();
      } else if (this.selectedAuditRating == 'Good') {
        newdate = new Date(new Date(date).setMonth(date.getMonth() + 12));
        month = newdate.getMonth();
        day = newdate.getDate();
        year = newdate.getFullYear();
      } else if (this.selectedAuditRating == 'Average') {
        newdate = new Date(new Date(date).setMonth(date.getMonth() + 6));
        month = newdate.getMonth();
        day = newdate.getDate();
        year = newdate.getFullYear();
      } else if (this.selectedAuditRating == 'Fail') {
        newdate = new Date(new Date(date).setMonth(date.getMonth() + 0));
        month = newdate.getMonth();
        day = newdate.getDate();
        year = newdate.getFullYear();
      }
      this.factoryForm.get('validUpto').setValue(new Date(year, month, day, 0, 0, 0, 0));
      console.log("validUpto", this.factoryForm.get('validUpto').value);
      // this.rec.validUpto = new Date(year, month, day, 0, 0, 0, 0);
      this.afterAuditDate = true;
    }
  }

  public calAuditRating(score): void {
    if (score == 0) {
      this.selectedAuditRating = 'Fail';
      this.afterAuditScore = true;
    }
    else if (score == '') {
      this.selectedAuditRating = '';
      this.afterAuditScore = false;
      console.log("score:", score)
    } else if (score >= 81 && score <= 100) {
      this.selectedAuditRating = 'Excellent';
      this.afterAuditScore = true;
    } else if (score >= 56 && score <= 80) {
      this.selectedAuditRating = 'Good';
      this.afterAuditScore = true;
    } else if (score >= 46 && score <= 55) {
      this.selectedAuditRating = 'Average';
      this.afterAuditScore = true;
    } else if (score <= 45) {
      this.selectedAuditRating = 'Fail';
      this.afterAuditScore = true;
    }
    this.factoryForm.get('auditRating').setValue(this.selectedAuditRating);
    this.onAuditDateChange();
  }


  public close(isRefresh: boolean): void {
    this._dialogRef.close(isRefresh);
  }

  ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}