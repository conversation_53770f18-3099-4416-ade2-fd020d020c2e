import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const DEFECT_TYPE_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. AUD001',
        topGroupTitleIcon: 'pi-user',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Auditor Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    name: {
       type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Ex. John',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    category: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [],
        value: "",
        label: "Category",
        placeholder: 'Select Category',
        show: true,
        rules: {
            required: true,
        }
    },
    defectNature: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [],
        value: "",
        label: "Type",
        placeholder: 'Select Type',
        show: true,
        rules: {
            required: true,
        }
    },
}