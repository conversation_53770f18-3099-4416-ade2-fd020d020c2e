export interface iTechnician {
  uuid?: string;
  code: string;
  id?: number;
  userName?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  country?: string;
  pincode?: string;
  contactNo?: string;
  email?: string;
  role?: string;
  createdDate?: number;
  modifiedDate?: number;
  status?: number;
  isActive?: boolean;
  mappedWith?: number;
  altemail?: string;
  createdDateText?: string; // For display purposes
}

export class Technician implements iTechnician {
  uuid: string = '';
  code: string = '';
  id: number = 0;
  userName: string = '';
  password: string = '';
  firstName: string = '';
  lastName: string = '';
  fullName: string = '';
  addressLine1: string = '';
  addressLine2: string = '';
  city: string = '';
  country: string = '';
  pincode: string = '';
  contactNo: string = '';
  email: string = '';
  role: string = '';
  createdDate: number = 0;
  modifiedDate: number = 0;
  status: number = 0;
  isActive: boolean = true;
  mappedWith: number = 0;
  altemail: string = '';
  createdDateText: string = '';
}
