
export interface iDefectType {
  code?: string;     // unique ID for the defect
  id?  : number;
  category?: string;
  name?: string;
  description?: string;  // defect name
  defectNature?: string;
  createdDate?: number;  // 
  modifiedDate?: number;
  status?: number;
  uuid?:string;
  createdDateText?: string; // For display purposes
};

export class DefectType implements iDefectType {
    public code?: string;
    public id?  : number;
    public category?: string;
    public name?: string;
    public description?: string;
    public defectNature?: string;
    public createDate?: number;
    public modifiedDate?: number;
    public status?: number;
    public uuid?:string;
    public createdDateText?: string; // For display purposes
};

export const FORM_IDS = {
  "PACAUDIT": {
    PACAUDIT_NO_OF_PACKAGES_ID: "*******",
    SAMPLE_SIZE_ID: "*******",
    ALLOWED_CRITICAL_ID: "*******",
    ALLOWED_MAJOR_ID: "*******",
    ALLOWED_MINOR_ID: "*******",
    FOUND_CRITICAL_ID: "*******",
    FOUND_MAJOR_ID: "*******",
    FOUND_MINOR_ID: "*******",
    RESULT_ID: "*******0",
  },
  "MEASUREMENT": {
    MEASUREMENT_NO_OF_SKU_ID: "*******",
    SAMPLE_SIZE_ID: "*******",
    ALLOWED_CRITICAL_ID: "*******",
    ALLOWED_MAJOR_ID: "*******",
    ALLOWED_MINOR_ID: "*******",
    FOUND_CRITICAL_ID: "*******",
    FOUND_MAJOR_ID: "*******",
    FOUND_MINOR_ID: "*******",
    RESULT_ID: "*******0",
  },
  "VISUALQUALITY": {
    VISUAL_QUALITY_QUANTITY_ID: "*******",
    SAMPLE_SIZE_ID: "*******",
    ALLOWED_CRITICAL_ID: "1.2.3.3",
    ALLOWED_MAJOR_ID: "1.2.3.4",
    ALLOWED_MINOR_ID: "1.2.3.5",
    FOUND_CRITICAL_ID: "1.2.3.6",
    FOUND_MAJOR_ID: "1.2.3.7",
    FOUND_MINOR_ID: "1.2.3.8",
    RESULT_ID: "*******0",
    SUMMARY_VISUAL_AQL_ID: "*******1",
  }
}

