import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { DefectType } from 'app/core/models/defect-type.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReplaySubject, takeUntil } from 'rxjs';

import { DEFECT_TYPE_FORM_MODEL } from './defect-type-form.model';

@Component({
  selector: 'tps-create-edit-defect-type',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-defect-type.component.html',
  styleUrl: './create-edit-defect-type.component.scss'
})
export class CreateEditDefectTypeComponent implements OnInit, OnDestroy {
  public hdr: string = 'Create Auditor';
  public defectTypeForm: FormGroup;
  public fields: any[] = [];
  public categories: any[] = [];
  public defectNatures: any[] = [];
  public rec: DefectType = new DefectType();
  isMobile: boolean = false;

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;

  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;
    this.buildForm();
    this.getCategories();
    this.getDefectNatures();
    this.configureForm();
  }

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(DEFECT_TYPE_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(DEFECT_TYPE_FORM_MODEL);
    this.defectTypeForm = new FormGroup(formGroupFields);
  }

  private getCategories(): void {
    // todo:add correct api
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.defectType.getCategoryDropDown)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.categories = response;
          this.prepareCategories();
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareCategories(): void {
    const categoryField = this.fields.find(field => field.name === 'category');
    if (categoryField) {
      categoryField.options = this.categories.map(role => {
        return {
          label: role.name,
          value: role.uuid
        };
      });
    }
  }

  private getDefectNatures(): void {
    // todo:add correct api
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.defectType.getDefectNatureDropDown)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.defectNatures = response;
          this.prepareDefectNatures();
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareDefectNatures(): void {
    const defectNatureField = this.fields.find(field => field.name === 'defectNature');
    if (defectNatureField) {
      defectNatureField.options = this.defectNatures.map(role => {
        return {
          label: role.name,
          value: role.uuid
        };
      });
    }
  }

  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Defect Type";
        this.setDataToForm();
        this.defectTypeForm.controls['code'].disable();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View Defect Type";
        this.setDataToForm();
        this.defectTypeForm.disable();
        break;
      default:
        break;
    }
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    this.defectTypeForm = this._formFactoryService.setFormControlsValues(this.defectTypeForm, this.rec, this.fields);
  }

  //submit form
  public onSubmit() {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.defectTypeForm, this.rec, this.fields);

    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update this defect type?`, this.rec.name)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.defectType.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Defect type updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the defect type details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    } else {
      this._commonService.confirm(`Are you sure you want to create a new  defect type?`, this.rec.name)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.defectType.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New defect type created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the defect type details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }

}
