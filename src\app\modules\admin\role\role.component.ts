import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { UserRole } from 'app/core/models/user-role.model';
import { SharedModule } from 'app/shared/shared.module';
import {
    actionType,
    buttonActions,
    CommonService,
    FORM_CONTROL_TYPES,
    FormFactoryService,
    HTTP_STATUS,
    ICON_BUTTON_SEVERITY,
    ICON_PRIMENG_LIST,
    InvokeService,
    TABLE_ACTION_TYPES,
    toolbarActions,
} from 'app/shared/tapas-ui';
import { ReplaySubject, takeUntil } from 'rxjs';

import { ROLE_FORM_MODEL } from './role-form.model';


@Component({
  selector: 'tps-role',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './role.component.html',
  styleUrl: './role.component.scss'
})
export class RoleComponent implements OnInit, OnDestroy {
  hdr: string = 'Roles';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  userRoles: UserRole[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];
  showDialog: boolean = false;
  dialogHeader: string = 'Create a Role';
  roleForm: FormGroup;
  fields: any;
  rec: UserRole = new UserRole();
  status: string;

  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  constructor(
    private _commonService: CommonService,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }


  public ngOnInit(): void {
    this.prepareTableActions();
    this.onRefresh();
    this.buildForm();
  }


  private prepareTableActions(): void {
    this.buttonActions = [
      // {
      //   name: 'Create Role',
      //   icon: 'pi-plus',
      //   primary: true,
      //   isSvg: false,
      //   show: this.ACL_LIST.ROLE_MASTER_CREATE,
      //   command: (action) => {
      //     this.configureForm(TABLE_ACTION_TYPES.CREATE, '');
      //   }
      // }
    ];
    this.toolBarActions = [];
  }
  public onRefresh(): void {
    this.getUserRoles();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Created On",
        field: "createdTimeText",
        sortable: true,
      },
      {
        headerName: "Role Name",
        field: "roleName",
        sortable: true,
      },
      {
        headerName: "Role Type",
        field: "roleType",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    //assign proper icons for each action
    iconsList.push({ type: TABLE_ACTION_TYPES.VIEW, icon: ICON_PRIMENG_LIST.PI_EYE, title: "View", data: row, severity: ICON_BUTTON_SEVERITY.SECONDARY, onAction: (rowItem) => this.configureForm(TABLE_ACTION_TYPES.VIEW, row) });
    // if (row.roleType != this._commonService.getLoggedInUser().role) {
    //   if (this.ACL_LIST?.ROLE_MASTER_UPDATE) {
    //     iconsList.push({ type: TABLE_ACTION_TYPES.EDIT, icon: ICON_PRIMENG_LIST.PI_PENCIL, title: "Edit", data: row, severity: ICON_BUTTON_SEVERITY.INFO, onAction: (rowItem) => this.configureForm(TABLE_ACTION_TYPES.EDIT, row) });
    //   }
    //   if (this.ACL_LIST?.ROLE_MASTER_DELETE) {
    //     iconsList.push({ type: TABLE_ACTION_TYPES.DELETE, icon: ICON_PRIMENG_LIST.PI_TRASH, title: "Delete", data: row, severity: ICON_BUTTON_SEVERITY.DANGER, onAction: (rowItem) => this.onDeleteRole(row) });
    //   }
    // }
    return iconsList;
  }



  private getUserRoles(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.roles.getUserRoleList)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.prepareUserRoles(this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }

  private prepareUserRoles(data: any[]): void {
    this.userRoles = data.map(item => ({ createdTimeText: this._commonService.dateTimeFormat(item.createdTime), ...item }));
  }



  //Create and Edit Role

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(ROLE_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(ROLE_FORM_MODEL);
    this.roleForm = new FormGroup(formGroupFields);

  }
  //configure form
  private configureForm(status, row): void {
    this.roleForm.reset()
    this.status = status;
    this.rec = row;
    switch (status) {
      case TABLE_ACTION_TYPES.CREATE:
        this.showDialog = true;
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.dialogHeader = "Update Role";
        this.setDataToForm();
        this.roleForm.get("roleType").disable();
        this.showDialog = true;
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.dialogHeader = "View Role";
        this.setDataToForm();
        this.roleForm.disable();
        this.showDialog = true;
        break;

      default:
        break;
    }
  }

  private setDataToForm(): void {

    this.roleForm = this._formFactoryService.setFormControlsValues(this.roleForm, this.rec, this.fields);
  }

  public onSubmit(): void {
    let payload: UserRole = this.roleForm.value;
    payload.roleType = payload.roleType.toUpperCase();
    payload.roleName = payload.roleName.toUpperCase();
    payload.code = payload.roleType.toUpperCase();
    payload.role = payload.roleType.toUpperCase();
    payload.name = payload.roleName.toUpperCase();
    payload.description = payload.roleName;
    if (this.status == TABLE_ACTION_TYPES.EDIT) {
      payload.uuid = this.rec.uuid;
    }
    this._commonService.confirm(`Are you sure you want to ${this.status == TABLE_ACTION_TYPES.EDIT ? 'update' : 'create'} a role`, payload.roleType)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.roles.createORUpdate, '', payload)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                if (response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success(`New role created successfully`);
                  this.onDialogClose();
                } else if (response.code == 400) {
                  this._commonService.error(response.message);
                }
                else {
                  this._commonService.error('Failed to create the role details');
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            })
        }
      })
  }


  private onDeleteRole(row): void {
    this._commonService.confirm(`Are you sure you want to delete a role`, row.roleType)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.roles.delete, '', row)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                if (response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success(`${row.roleType} role deleted successfully`);
                  this.onRefresh();
                } else {
                  this._commonService.handleError(response);
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            })
        }
      })

  }

  public onDialogClose(): void {
    this.showDialog = false;
    this.roleForm.reset();
    this.status = '';
    this.onRefresh();
  }


  public ngOnDestroy(): void {
    this.$destroyed.next(true);
    this.$destroyed.complete();
  }

}
