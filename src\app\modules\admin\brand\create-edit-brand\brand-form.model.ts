import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const BRAND_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. BRD001',
        topGroupTitleIcon: 'pi-tag',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Brand Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    name: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Ex. Brand Name',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    subBrand: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Sub Brand",
        placeholder: 'Ex. Sub Brand',
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact No",
        placeholder: 'Ex. 1234567890',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 15,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: 'Ex. <EMAIL>',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    }
}