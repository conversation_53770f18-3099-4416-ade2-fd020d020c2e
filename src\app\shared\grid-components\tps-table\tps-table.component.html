<div class="w-full h-full flex flex-col gap-4 " style="gap:1rem;padding:1rem">
    <div class="w-full flex flex-wrap flex-row justify-between" style="justify-content: space-between;"
        *ngIf="showTableHeader">
        <div class="flex flex-row gap-6">
            <tps-header [headerName]="tableHeader" [showBackIcon]="showBackIcon" [showNoOfRows]="showNoOfRows"
                [tooltip]="headerTooltip" [totalNoOfRow]="tableDataCount"></tps-header>
        </div>
        <div class="flex flex-row flex-wrap gap-4 action_button_wrapper">
            <ng-container *ngFor="let buttonAction of buttonActions">
                <ng-container *ngIf="buttonAction.show">
                    <tps-primary-button *ngIf="buttonAction.primary" [icon]="buttonAction.icon"
                        [isSvg]="buttonAction.isSvg" [buttonName]="buttonAction.name"
                        (onClick)="buttonAction.command()"></tps-primary-button>

                    <tps-secondary-button *ngIf="!buttonAction.primary" [icon]="buttonAction.icon"
                        [isSvg]="buttonAction.isSvg" [buttonName]="buttonAction.name"
                        (onClick)="buttonAction.command()"></tps-secondary-button>
                </ng-container>
            </ng-container>

        </div>
    </div>
    <div class="w-full flex justify-center items-center" *ngIf="metaData" [innerHTML]="metaData">

    </div>
    <!-- Main Filter Form  -->
    <div class="w-full flex flex-row items-center flex-wrap gap-6" *ngIf="mainFilterFields.length>0">
        <form [formGroup]="mainFilterFormGroup" autocomplete="off">
            <div class="w-full flex flex-row flex-wrap  gap-4">
                <ng-container *ngFor="let field of mainFilterFields" [ngSwitch]="field.type">
                    <ng-container *ngIf="field.show">
                        <tps-text class="w-auto" style="max-width: 11rem;min-width: 11rem;"
                            *ngSwitchCase="FORM_CONTROL_TYPES.TEXT" [field]="field" [formName]="mainFilterFormGroup"
                            [layout]="'column'"></tps-text>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-number class="w-auto" style="max-width: 11rem;min-width: 11rem;"
                            *ngSwitchCase="FORM_CONTROL_TYPES.NUMBER" [field]="field" [formName]="mainFilterFormGroup"
                            [layout]="'column'"></tps-number>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-text-area class="w-auto" style="max-width: 11rem;min-width: 11rem;"
                            *ngSwitchCase="FORM_CONTROL_TYPES.TEXTAREA" [field]="field" [formName]="mainFilterFormGroup"
                            [layout]="'column'"></tps-text-area>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-single-select class="w-auto" style="max-width: 11rem;min-width: 11rem;"
                            *ngSwitchCase="FORM_CONTROL_TYPES.SINGLE_SELECT" [field]="field"
                            [formName]="mainFilterFormGroup" [layout]="'column'"></tps-single-select>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-multi-select class="w-60" *ngSwitchCase="FORM_CONTROL_TYPES.MULTI_SELECT" [field]="field"
                            [formName]="mainFilterFormGroup" [layout]="'column'"></tps-multi-select>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-checkbox class="w-auto" style="max-width: 11rem;min-width: 11rem;"
                            *ngSwitchCase="FORM_CONTROL_TYPES.CHECKBOX" [field]="field" [formName]="mainFilterFormGroup"
                            [layout]="'column'"></tps-checkbox>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-date-picker class="w-auto" style="max-width: 11rem;min-width: 11rem;"
                            *ngSwitchCase="FORM_CONTROL_TYPES.DATE" [field]="field" [formName]="mainFilterFormGroup"
                            [layout]="'column'"></tps-date-picker>
                    </ng-container>
                    <ng-container *ngIf="field.show">
                        <tps-radio class="w-auto" style="max-width: 11rem;min-width: 11rem;"
                            *ngSwitchCase="FORM_CONTROL_TYPES.RADIO" [field]="field" [formName]="mainFilterFormGroup"
                            [layout]="'column'"></tps-radio>
                    </ng-container>

                    <ng-container *ngIf="field.rightSideGap">
                        <div class="w-auto">
                        </div>
                    </ng-container>
                </ng-container>
            </div>
        </form>
        <div class="flex flex-col gap-1" style="margin-top: -5px;">
            <div class="visibilityHidden">Filter</div>
            <tps-primary-button [isDisabled]="mainFilterFormGroup.invalid" [buttonName]="'GO'"
                (onClick)="onMainFilterChange()  "></tps-primary-button>
        </div>
    </div>

    <div class="w-full  h-full flex flex-col">
        <div class="w-full tps-table-action-bar flex  sm:flex-row gap-4 sm:gap-0">
            <div class=" sm:w-auto">
                <span class="p-input-icon-left tfl-search_box w-full sm:w-auto" *ngIf="rowData?.length>0">
                    <p-iconfield>
                        <p-inputicon styleClass="pi pi-search" />
                        <input type="text" class="w-full sm:w-[300px] h-[32px]" pInputText placeholder="Search"
                            [(ngModel)]="filterText" pTooltip="Search by value" class="search-text"
                            (input)="onFilterTextBoxChanged($event)" />
                    </p-iconfield>
                </span>
            </div>
            <div class=" sm:w-auto flex flex-row justify-center sm:justify-end gap-4">
                <i *ngIf="isShowQuickFilter" pBadge style="font-size: 1.5rem" severity="warning"
                    [value]="showActiveFiltersCount" class="pi pi-filter action-icon"
                    (click)="quickFilter.toggle($event)" [pTooltip]="'Quick Filter'" tooltipPosition="bottom"></i>

                <img *ngIf="showRefreshIcon" class="cursor-pointer w-5 h-5" src="assets/svg/refresh_blue.svg"
                    [pTooltip]="'Refresh'" tooltipPosition="bottom" (click)="onViewRefresh()" />

                <ng-container *ngFor="let actionItem of toolBarActions">
                    <img *ngIf="actionItem.svgIcon" class="cursor-pointer w-5 h-5"
                        src="assets/svg/{{actionItem.svgIcon}}.svg" [pTooltip]="actionItem.name"
                        tooltipPosition="bottom" (click)="actionItem.command(actionItem)" />

                    <i *ngIf="!actionItem.svgIcon" class="pi {{actionItem.icon}} action-icon"
                        [pTooltip]="actionItem.name" tooltipPosition="bottom"
                        (click)="actionItem.command(actionItem)"></i>
                </ng-container>
            </div>
        </div>
        <div class="w-full h-full tps-table-body">
            <div class="tps-table-container w-full h-full">
                <!-- Desktop View -->
                <div class="desktop-view w-full h-full" *ngIf="!isMobile">
                    <p-table *ngIf="filteredData?.length > 0 && !isLoading" [columns]="columnsDef"
                        [value]="filteredData" [(selection)]="selection" [showGridlines]="true" [rowHover]="true"
                        [selectionMode]="selectionMode" size="small" [scrollable]="true" [virtualScroll]="true"
                        [lazy]="false" scrollHeight="flex" [tableStyle]="{ 'min-width': '50rem' }" [paginator]="true"
                        [rows]="itemsPerPage" [first]="first" [showCurrentPageReport]="true"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                        (onPage)="pageChange($event)">
                        <ng-template #header let-columns class="tps-table-header">
                            <tr>
                                <th *ngIf="showSelectionColumn" style="width: 50px">
                                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                                </th>
                                <th *ngFor="let col of columns" [pSortableColumn]="col.sortable ? col.field : null"
                                    [style.width]="col.width +'px'" [style.minWidth]="col.minWidth +'px'"
                                    [style.maxWidth]="col.maxWidth +'px'" [pTooltip]="col.headerName"
                                    tooltipPosition="bottom" [frozen]="col.isActionCol" [alignFrozen]="col.pinned"
                                    [pFrozenColumn]="col.isActionCol ? true : false">
                                    {{col.headerName}}
                                    <p-sortIcon *ngIf="col.sortable" [field]="col.field"></p-sortIcon>
                                </th>
                            </tr>
                        </ng-template>

                        <ng-template #body let-rowData let-columns="columns" class="tps-table-body">
                            <tr class="tps-table-row">
                                <td *ngIf="showSelectionColumn" style="width: 50px">
                                    <p-tableCheckbox [value]="rowData"></p-tableCheckbox>
                                </td>
                                <td *ngFor="let col of columns" [style.width]="col.width +'px'"
                                    [style.minWidth]="col.minWidth +'px'" [style.maxWidth]="col.maxWidth +'px'"
                                    [frozen]="col.isActionCol" [alignFrozen]="col.pinned"
                                    [pFrozenColumn]="col.isActionCol ? true : false">
                                    <ng-container [ngSwitch]="true">
                                        <ng-container *ngSwitchCase="col.isActionCol">
                                            <tps-cell-action [actions]="col.actions(rowData)"></tps-cell-action>
                                        </ng-container>

                                        <ng-container *ngSwitchCase="col.isTemplate">
                                            <tps-html-cell [htmlContent]="rowData[col.field]"></tps-html-cell>
                                        </ng-container>

                                        <ng-container *ngSwitchDefault>
                                            <div [pTooltip]="rowData[col.field]" tooltipPosition="bottom">{{
                                                rowData[col.field]}}</div>
                                        </ng-container>
                                    </ng-container>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template #emptymessage>
                            <tr>
                                <td colspan="100%" class="text-center w-full h-full">
                                    <div class="w-full h-full flex flex-col items-center justify-center">
                                        <img class="no-data-img" src="./assets/images/ui/no-data.png" alt="">
                                        <div class="no-data-ava">No Data available</div>
                                        <div class="no-data-label"> No Data is available at the moment for <span
                                                *ngIf="tableHeader">{{tableHeader}}</span></div>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <p-table *ngIf="isLoading" [columns]="columnsDef" [value]="[1,2,3,4,5,6,7,8,9]" [rowHover]="true"
                        size="small" [scrollable]="true" [tableStyle]="{ 'min-width': '50rem','max-height': height }">
                        <ng-template pTemplate="header" let-columns class="tps-table-header">
                            <tr>
                                <th *ngFor="let col of columns" [style.width]="col.width" [pTooltip]="col.headerName"
                                    tooltipPosition="bottom">
                                    {{col.headerName}}

                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-rowData let-columns="columns" class="tps-table-body">
                            <tr class="tps-table-row">
                                <td *ngFor="let col of columns" [style.width]="col.width"
                                    [pTooltip]="rowData[col.field]" tooltipPosition="bottom">
                                    <p-skeleton height="1rem" styleClass="mb-2" />
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>

                <!-- Mobile/Tablet View -->
                <div class="mobile-view mt-2" *ngIf="isMobile">
                    <div class="mobile-cards-grid">
                        <!-- Loading State -->
                        <ng-container *ngIf="isLoading">
                            <div class="mobile-card skeleton-mobile" *ngFor="let i of [1,2,3,4]">
                                <div class="card-header">
                                    <p-skeleton height="1.5rem" width="70%"></p-skeleton>
                                </div>
                                <div class="card-body">
                                    <div class="flex flex-row justify-between" *ngFor="let col of columnsDef">
                                        <div class="field-label">{{col.headerName}}</div>
                                        <div class="field-value">
                                            <p-skeleton height="1rem"></p-skeleton>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>

                        <!-- Data State -->
                        <ng-container *ngIf="!isLoading && filteredData?.length > 0">
                            <div class="card flex flex-col gap-2 mb-2"
                                [ngClass]="{'selected-row': getSelectedRow(rowData)}"
                                style="box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;"
                                *ngFor="let rowData of filteredData">
                                <div class="flex flex-row items-center gap-1 pl-2 pr-2 pt-1 pb-1"
                                    *ngIf="showSelectionColumn">
                                    <p-checkbox [checked]="getSelectedRow(rowData)" [value]="rowData"
                                        (onChange)="onSelectChanged(rowData)" [inputId]="rowData" />
                                    <label [for]="rowData" class="ml-2"><span
                                            class="font-bold ml-2">{{rowData[columnsDef[0].field]}}</span></label>
                                </div>
                                <div class="font-bold pl-2 pr-2 pt-1 pb-1" *ngIf="!showSelectionColumn">
                                    {{rowData[columnsDef[0].field]}}</div>
                                <div class="flex flex-col">
                                    <ng-container *ngFor="let col of columnsDef">
                                        <!-- Skip action column and first column (shown in header) -->
                                        <ng-container *ngIf="!col.isActionCol && col !== columnsDef[0]">
                                            <div class="flex flex-row justify-between pl-2 pr-2 pt-1 pb-1"
                                                style="border-bottom: 1px solid #E7EAF0;">
                                                <div class="field-label">{{col.headerName}}</div>
                                                <div class="field-value">
                                                    <ng-container [ngSwitch]="true">
                                                        <ng-container *ngSwitchCase="col.isTemplate">
                                                            <tps-html-cell
                                                                [htmlContent]="rowData[col.field]"></tps-html-cell>
                                                        </ng-container>
                                                        <ng-container *ngSwitchDefault>
                                                            {{rowData[col.field]}}
                                                        </ng-container>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </ng-container>
                                    </ng-container>
                                </div>

                                <!-- Action buttons -->
                                <div class="flex flex-row justify-center items-center gap-2 card-actions m-2"
                                    *ngIf="actionColumn"
                                    style="border: 1px solid #517EBC;border-radius: 4px; padding: 0.25rem;">
                                    <tps-cell-action [actions]="actionColumn.actions(rowData)"
                                        [isMobile]="isMobile"></tps-cell-action>
                                </div>
                            </div>
                        </ng-container>


                    </div>
                </div>
                <!-- No Data State -->
                <div class="w-full h-full" *ngIf="!isLoading && filteredData?.length === 0">
                    <div class="w-full h-full flex flex-col items-center justify-center">
                        <img class="no-data-img" src="./assets/images/ui/no-data.png" alt="">
                        <div class="no-data-ava">No Data available</div>
                        <div class="no-data-label">
                            No Data is available at the moment for
                            <span *ngIf="tableHeader">{{tableHeader}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Quick Filter Form Modal  -->
<p-popover *ngIf="isShowQuickFilter" #quickFilter [dismissable]="false">
    <div class="w-full flex flex-column gap-3 w-50rem">
        <div class="w-full flex flex row justify-between">
            <div class="no-data-label">Quick Filter</div>
        </div>
        <div class="w-full modal-content" style="padding: 0px !important;" *ngIf="fields.length>0">
            <form [formGroup]="filterFormGroup" autocomplete="off">
                <div class="w-full flex flex-col gap-4">
                    <div *ngIf="status!=TABLE_ACTION_TYPES.VIEW" class="required_fields_message mb-2">
                        Note: Fields marked with (*) are mandatory
                    </div>
                    <div class="dynamic-form-wrapper w-full min-w-0 form-row justify-between ">
                        <ng-container *ngFor="let field of fields" [ngSwitch]="field.type">
                            <ng-container *ngIf="field.show">
                                <tps-text class="col-sm-5 col-5" *ngSwitchCase="FORM_CONTROL_TYPES.TEXT" [field]="field"
                                    [formName]="filterFormGroup" [layout]="'column'"></tps-text>
                            </ng-container>

                            <ng-container *ngIf="field.show">
                                <tps-number class="col-sm-5 col-5" *ngSwitchCase="FORM_CONTROL_TYPES.NUMBER"
                                    [field]="field" [formName]="filterFormGroup" [layout]="'column'"></tps-number>
                            </ng-container>

                            <ng-container *ngIf="field.show">
                                <tps-text-area class="col-sm-5 col-5" *ngSwitchCase="FORM_CONTROL_TYPES.TEXTAREA"
                                    [field]="field" [formName]="filterFormGroup" [layout]="'column'"></tps-text-area>
                            </ng-container>

                            <ng-container *ngIf="field.show">
                                <tps-single-select class="col-sm-5 col-5"
                                    *ngSwitchCase="FORM_CONTROL_TYPES.SINGLE_SELECT" [field]="field"
                                    [formName]="filterFormGroup" [layout]="'column'"></tps-single-select>
                            </ng-container>

                            <ng-container *ngIf="field.show">
                                <tps-multi-select class="col-sm-5 col-5" *ngSwitchCase="FORM_CONTROL_TYPES.MULTI_SELECT"
                                    [field]="field" [formName]="filterFormGroup" [layout]="'column'"></tps-multi-select>
                            </ng-container>

                            <ng-container *ngIf="field.show">
                                <tps-checkbox class="col-sm-5 col-5" *ngSwitchCase="FORM_CONTROL_TYPES.CHECKBOX"
                                    [field]="field" [formName]="filterFormGroup" [layout]="'column'"></tps-checkbox>
                            </ng-container>

                            <ng-container *ngIf="field.show">
                                <tps-date-picker class="col-sm-5 col-5" *ngSwitchCase="FORM_CONTROL_TYPES.DATE"
                                    [field]="field" [formName]="filterFormGroup" [layout]="'column'"></tps-date-picker>
                            </ng-container>
                            <ng-container *ngIf="field.show">
                                <tps-radio class="col-sm-5 col-5" *ngSwitchCase="FORM_CONTROL_TYPES.RADIO"
                                    [field]="field" [formName]="filterFormGroup" [layout]="'column'"></tps-radio>
                            </ng-container>

                            <ng-container *ngIf="field.rightSideGap">
                                <div class="col-sm-5 col-5">
                                </div>
                            </ng-container>
                        </ng-container>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <div class="w-full flex flex-row justify-end gap-6">
                <tps-secondary-button [icon]="'pi-times'" [buttonName]="'Cancel'"
                    (onClick)="quickFilter.toggle($event)"></tps-secondary-button>
                <tps-primary-button [icon]="'pi-filter'" [buttonName]="'Filter'"
                    (onClick)="onClickQuickFilter()  "></tps-primary-button>
            </div>
        </div>
    </div>
</p-popover>