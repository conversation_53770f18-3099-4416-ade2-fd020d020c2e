export interface iVendor {
  uuid?: string;
  code: string;
  id?: number;
  name?: string;
  description?: string;
  contactPerson?: string;
  contactNo?: string;
  email?: string;
  altemail?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  country?: string;
  pincode?: string;
  website?: string;
  gstNumber?: string;
  panNumber?: string;
  establishedYear?: number;
  createdDate?: number;
  modifiedDate?: number;
  status?: number;
  isActive?: boolean;
  createdDateText?: string; // For display purposes
}

export class Vendor implements iVendor {
  uuid: string = '';
  code: string = '';
  id: number = 0;
  name: string = '';
  description: string = '';
  contactPerson: string = '';
  contactNo: string = '';
  email: string = '';
  altemail: string = '';
  addressLine1: string = '';
  addressLine2: string = '';
  city: string = '';
  country: string = '';
  pincode: string = '';
  website: string = '';
  gstNumber: string = '';
  panNumber: string = '';
  establishedYear: number = 0;
  createdDate: number = 0;
  modifiedDate: number = 0;
  status: number = 0;
  isActive: boolean = true;
  createdDateText: string = '';
}
