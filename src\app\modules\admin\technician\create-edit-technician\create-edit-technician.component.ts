import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Technician } from 'app/core/models/technician.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { Util } from 'app/core/common/util';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';

import { TECHNICIAN_FORM_MODEL } from './technician-form.model';

@Component({
  selector: 'tps-create-edit-technician',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-technician.component.html'
})
export class CreateEditTechnicianComponent implements OnInit, OnD<PERSON>roy {
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  technicianForm: FormGroup;
  fields: any = {};
  rec: Technician = new Technician();
  status: string = TABLE_ACTION_TYPES.CREATE;
  TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  isMobile: boolean = false;

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _formBuilder: FormBuilder,
    private _formFactoryService: FormFactoryService,
    private _dialogRef: DynamicDialogRef,
    private _dialogConfig: DynamicDialogConfig,
  ) {
    this.technicianForm = this._formBuilder.group({});
  }

  public ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.rec = this._dialogConfig.data.data;
    this.status = this._dialogConfig.data.status;
    this.prepareForm();
    this.loadRoles();
  }

  private prepareForm(): void {
    const formModel = Util.clone(TECHNICIAN_FORM_MODEL);

    // Hide password field for edit and view modes
    if (this.status === TABLE_ACTION_TYPES.EDIT || this.status === TABLE_ACTION_TYPES.VIEW) {
      formModel.password.show = false;
    }

    // Build form using FormFactoryService
    const formGroupFields = this._formFactoryService.getFormControlsFields(formModel);
    this.fields = this._formFactoryService.getFieldsList(formModel);
    this.technicianForm = new FormGroup(formGroupFields);

    // Set existing values if editing
    if (this.rec && (this.status === TABLE_ACTION_TYPES.EDIT || this.status === TABLE_ACTION_TYPES.VIEW)) {
      this.technicianForm = this._formFactoryService.setFormControlsValues(this.technicianForm, this.rec, this.fields);
    }
  }

  private loadRoles(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.roles.getUserRoleList)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.fields.role.options = response.map(role => ({
            label: role.name,
            value: role.name
          }));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  public onSubmit(): void {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.technicianForm, this.rec, this.fields);
    this.rec.fullName = `${this.rec.firstName} ${this.rec.lastName}`.trim();

    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update a technician`, this.rec.fullName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.administration.technician.update.paramList.uuid = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.technician.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Technician updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the technician details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    } else {
      this._commonService.confirm(`Are you sure you want to create a technician`, this.rec.fullName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.technician.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New technician created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the technician details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    }
  }

  public close(response: boolean): void {
    this._dialogRef.close(response);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
