import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const VENDOR_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. VEN001',
        topGroupTitleIcon: 'pi-building',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Vendor Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    name: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Ex. Vendor Name',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    description: {
        type: FORM_CONTROL_TYPES.TEXTAREA,
        value: "",
        label: "Description",
        placeholder: 'Ex. Vendor Description',
        show: true,
        rules: {
            maxLength: 500,
        }
    },
    contactPerson: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact Person",
        placeholder: 'Ex. <PERSON>e',
        topGroupTitle: 'Contact Details',
        topGroupTitleIcon: 'pi-phone',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact No",
        placeholder: 'Ex. 1234567890',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 15,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: 'Ex. <EMAIL>',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    }
}
