import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const VENDOR_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. VEN001',
        topGroupTitleIcon: 'pi-building',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Vendor Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    name: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Ex. ABC Vendor Ltd.',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    description: {
        type: FORM_CONTROL_TYPES.TEXTAREA,
        value: "",
        label: "Description",
        placeholder: 'Ex. Brief description about vendor',
        show: true,
        rules: {
            maxLength: 500,
        }
    },
    contactPerson: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact Person",
        placeholder: 'Ex. John Doe',
        topGroupTitle: 'Contact Details',
        topGroupTitleIcon: 'pi-user',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact No",
        placeholder: 'Ex. +1234567890',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 15,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: 'Ex. <EMAIL>',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    altemail: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Alternative Email",
        placeholder: 'Ex. <EMAIL>',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    website: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Website",
        placeholder: 'Ex. www.vendor.com',
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    addressLine1: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Address Line 1",
        placeholder: 'Ex. 123 Main Street',
        topGroupTitle: 'Address Details',
        topGroupTitleIcon: 'pi-map-marker',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    addressLine2: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Address Line 2",
        placeholder: 'Ex. Suite 100',
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    city: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "City",
        placeholder: 'Ex. New York',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    country: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Country",
        placeholder: 'Ex. USA',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    pincode: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "PIN Code",
        placeholder: 'Ex. 123456',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 10,
        }
    },
    gstNumber: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "GST Number",
        placeholder: 'Ex. 22AAAAA0000A1Z5',
        topGroupTitle: 'Business Details',
        topGroupTitleIcon: 'pi-file',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            maxLength: 15,
        }
    },
    panNumber: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "PAN Number",
        placeholder: 'Ex. **********',
        show: true,
        rules: {
            maxLength: 10,
        }
    },
    establishedYear: {
        type: FORM_CONTROL_TYPES.NUMBER,
        value: "",
        label: "Established Year",
        placeholder: 'Ex. 2000',
        show: true,
        rules: {
            min: 1900,
            max: new Date().getFullYear(),
        }
    },
    isActive: {
        type: FORM_CONTROL_TYPES.CHECKBOX,
        value: true,
        label: "Active",
        show: true,
    }
}
