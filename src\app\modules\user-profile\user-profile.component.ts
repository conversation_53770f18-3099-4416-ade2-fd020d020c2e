import { ChangeDetectionStrategy, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { AuthService } from 'app/core/auth/auth.service';
import { Util } from 'app/core/common/util';
import { iUser, QiUser } from 'app/core/models/user.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { ChipModule } from 'primeng/chip';
import { ReplaySubject, takeUntil } from 'rxjs';

import { USER_FORM_MODEL } from './user-profile.model';



@Component({
  selector: 'app-user-profile',
  standalone: true,
  imports: [SharedModule, ChipModule],
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserProfileComponent implements OnInit {
  fields: any[] = [];
  public rec: iUser = new QiUser();
  hdr: string = "User Profile";
  status: string;
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  //form
  userProfileForm: FormGroup;
  public extensions: string = ".png,.jpeg,.jpg";
  public message: string = `Please use the ${this.extensions} format to upload the signature.`;
  public visible: boolean = false;
  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public MFA_LIST = [{ label: 'Yes', value: 1 }, { label: 'No', value: 0 }]
  constructor(
    private _authService: AuthService,
    private _commonService: CommonService,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  public ngOnInit(): void {
    this.rec = JSON.parse(sessionStorage.getItem("currentUser"));
    console.log("rec:", this.rec)
    this.buildForm();
    this.onChangeTab({ index: 0 });
  }
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(USER_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(USER_FORM_MODEL);
    this.userProfileForm = new FormGroup(formGroupFields);
    this.fields = this._formFactoryService.setOptionsToDropDown(this.fields, 'mfaEnable', this.MFA_LIST);
    this.setFormData();
  }
  private setFormData(): void {
    this.userProfileForm = this._formFactoryService.setFormControlsValues(this.userProfileForm, this.rec, this.fields);
    this.userProfileForm.get("userName").disable();
    this.userProfileForm.get("role").disable();
    this.userProfileForm.get("mfaEnable").disable();
  }

  public onChangeTab(event): void {
    switch (event.index) {
      case 0:
        this.fields.forEach(item => item.show = false);
        this.fields = this._formFactoryService.showHideField(this.fields, 'firstName', true);
        this.fields = this._formFactoryService.showHideField(this.fields, 'lastName', true);
        this.fields = this._formFactoryService.showHideField(this.fields, 'userName', true)
        break;
      case 1:
        this.fields.forEach(item => item.show = false);
        this.fields = this._formFactoryService.showHideField(this.fields, 'password', true);
        this.fields = this._formFactoryService.showHideField(this.fields, 'cPassword', true);
        break;

      case 2:
        this.fields.forEach(item => item.show = false);
        this.fields = this._formFactoryService.showHideField(this.fields, 'contactNo', true);
        this.fields = this._formFactoryService.showHideField(this.fields, 'email', true);
        break;
      case 3:
        this.fields.forEach(item => item.show = false);
        this.fields = this._formFactoryService.showHideField(this.fields, 'city', true);
        this.fields = this._formFactoryService.showHideField(this.fields, 'country', true);
        this.fields = this._formFactoryService.showHideField(this.fields, 'pinCode', true);
        break;
      case 4:
        this.fields.forEach(item => item.show = false);
        this.fields = this._formFactoryService.showHideField(this.fields, 'mfaEnable', true);
        break;
      default:
        break;
    }
  }


  public onSubmit(): void {
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.userProfileForm, this.rec, this.fields);
    this.rec.fullName = this.rec.firstName ? this.rec.lastName ? `${this.rec.firstName} ${this.rec.lastName}` : this.rec.firstName : '';
    if (this.userProfileForm.get("cPassword").value) {
      this.rec.password = this.userProfileForm.get("cPassword").value;
    }
    this._commonService.confirm(`Are you sure you want to update your user details?`).subscribe({
      next: response => {
        if (response) {
          APP_UI_CONFIG.administration.users.update.paramList.uuid = this.rec.uuid;
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.users.update, '', Util.clone(this.rec))
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                if (response.code == HTTP_STATUS.SUCCESS) {
                  this.visible = false;
                  this._commonService.warning('Your user information has been successfully updated, and you will be logged out. Please log in again');
                  setTimeout(() => {
                    this._authService.signOut();
                  }, 1000)
                }
                else {
                  this._commonService.handleError(response);
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            })
        }
      }
    })
  }

  public close(): void {
    this._commonService.historyBack();
  }
}

