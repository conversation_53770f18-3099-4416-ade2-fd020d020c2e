import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const AUDITOR_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. AUD001',
        topGroupTitleIcon: 'pi-user',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Auditor Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    userName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Username",
        placeholder: 'Ex. john123',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_NO_SPACE,
            minLength: 5,
            maxLength: 20,
        },
        tooltip: "Username should be alphabets and numbers (No space allowed)"
    },
    password: {
        type: FORM_CONTROL_TYPES.PASSWORD,
        value: "",
        label: "Password",
        placeholder: 'Ex. Password123',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    firstName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "First Name",
        placeholder: 'Ex. John',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    lastName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Last Name",
        placeholder: 'Ex. Doe',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    role: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [],
        value: "",
        label: "Role",
        placeholder: 'Select role',
        show: true,
        rules: {
            required: true,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: 'Ex. <EMAIL>',
        topGroupTitle: 'Contact Details',
        topGroupTitleIcon: 'pi-phone',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    altemail: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Alternative Email",
        placeholder: 'Ex. <EMAIL>',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact No.",
        placeholder: 'Ex. 1234567890',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            minLength: FORM_FIELDS_CONSTANTS_VALUES.PHONE_NUMBER_LENGTH,
            maxLength: FORM_FIELDS_CONSTANTS_VALUES.PHONE_NUMBER_LENGTH,
        }
    },
    addressLine1: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Address Line 1",
        placeholder: 'Ex. 123 Main St',
        topGroupTitle: 'Address',
        topGroupTitleIcon: 'pi-map-marker',
        severity: ICON_BUTTON_COLOR.HELP,
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    addressLine2: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Address Line 2",
        placeholder: 'Ex. Apt 4B',
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    city: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "City",
        placeholder: 'Ex. New York',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    country: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Country",
        placeholder: 'Ex. USA',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    pincode: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Pincode",
        placeholder: 'Ex. 123456',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 10,
        }
    },
    isActive: {
        type: FORM_CONTROL_TYPES.CHECKBOX,
        value: true,
        label: "Active",
        show: true,
    },
    mappedWith: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [],
        value: "",
        label: "Mapped With",
        placeholder: 'Select mapping',
        show: true,
        rules: {
            // Not required
        }
    }
}