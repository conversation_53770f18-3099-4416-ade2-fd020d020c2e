import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { UserRole } from 'app/core/models/user-role.model';
import { iUser, QiUser } from 'app/core/models/user.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Subject } from 'rxjs';
import { ReplaySubject, takeUntil } from 'rxjs';

import { USER_FORM_MODEL } from './user-form.model';


@Component({
  selector: 'tps-create-edit-user',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-user.component.html',
  styleUrl: './create-edit-user.component.scss'
})
export class CreateEditUserComponent implements OnInit, OnDestroy {
  public hdr: string = 'Create User';
  public userForm: FormGroup;
  public fields: any[] = [];
  public rec: iUser = new QiUser();
  isMobile: boolean = false;
  private destroy$ = new Subject<void>();

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;
  userRoles: UserRole[];
  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;
    this.buildForm();
    this.configureForm();
    this.getUserRoles();
  }
  private getUserRoles(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.roles.getUserRoleList)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.userRoles = response;
          this.prepareUserRoles();
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }
  private prepareUserRoles(): void {
    let userRoles = this.userRoles.map(role => ({ label: role.roleName, value: role.roleType, raw: role }));
    this.fields = this._formFactoryService.setOptionsToDropDown(this.fields, 'role', userRoles);
  }
  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(USER_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(USER_FORM_MODEL);
    this.userForm = new FormGroup(formGroupFields);
  }
  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update User";
        this.setDataToForm();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View User";
        this.setDataToForm();
        this.userForm.disable();
        break;
      default:
        break;
    }
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    this.userForm = this._formFactoryService.setFormControlsValues(this.userForm, this.rec, this.fields);
  }

  //submit form
  public onSubmit() {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.userForm, this.rec, this.fields);
    this.rec.fullName = this.rec.firstName ? this.rec.lastName ? `${this.rec.firstName} ${this.rec.lastName}` : this.rec.firstName : '';
    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update a user`, this.rec.userName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.administration.users.update.paramList.uuid = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.users.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`${this.rec.userName} user details updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the user details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    } else {
      this.rec.code = this.rec.userName;
      this._commonService.confirm(`Are you sure you want to create a user`, this.rec.userName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.users.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New user created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the user details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })

    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Add method to enable/disable controls
  public enableControl(controlName: string): void {
    this.userForm.get(controlName)?.enable();
  }

  public disableControl(controlName: string): void {
    this.userForm.get(controlName)?.disable();
  }
}
