import { BreakpointObserver, Breakpoints, BreakpointState } from '@angular/cdk/layout';
import { Location } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import {
  TpsConfirmationDialogComponent,
} from 'app/shared/dialogs/tps-confirmation-dialog/tps-confirmation-dialog.component';
import { TpsMessageDialogComponent } from 'app/shared/dialogs/tps-message-dialog/tps-message-dialog.component';
import { saveAs } from 'file-saver';
import moment from 'moment';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { BehaviorSubject, Observable, ReplaySubject, takeUntil } from 'rxjs';

import { TpsViewImageComponent } from '../tapas-ui';


@Injectable({
  providedIn: 'root'
})
export class CommonService {
  pattern = {
    TEXT_AREA_LENGTH: 250, // Changed from 500 to 250
    alphaNum: "^[0-9-a-z-A-Z( )]+$",
    EMAIL_PATTERN: "[a-zA-Z0-9.-_]{1,}@[a-zA-Z0-9.-]{2,}[.]{1}[a-zA-Z]{2,4}",
    NUMERIC_PATTERN: "^[0-9]+$",
    FLOAT_NUMBER_PATTERN: "^[0-9.]+$",
    ALPHA_PATTERN: "^[a-zA-Z](?:[a-zA-Z ]*[a-zA-Z])?$",
    ALPHA_NUMERIC_PATTERN_NO_SPACE: "^[a-zA-Z0-9]+$",
    ALPHA_NUMERIC_PATTERN_WITH_SPACE: "^[a-zA-Z0-9 ]+$",
  }
  emitData: BehaviorSubject<any> = new BehaviorSubject(null);
  loadingTableData: BehaviorSubject<any> = new BehaviorSubject(null);
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  constructor(
    private _sanitizer: DomSanitizer,
    private _dialogService: DialogService,
    public _router: Router,
    public route: ActivatedRoute,
    public _location: Location,
    private breakpointObserver: BreakpointObserver,

  ) {

  }

  setBasicAuth(basic) {
    sessionStorage.setItem('basicAuth', basic);
  }
  getBasicAuth(): any {
    return sessionStorage.getItem('basicAuth');
  }

  //get logged in user data
  public getLoggedInUser(): any {
    if (sessionStorage.hasOwnProperty('currentUser')) {
      return JSON.parse(sessionStorage.getItem('currentUser'));
    } else {
      return null;
    }
  }

  public getUseRole(): string {
    const currentUser: any = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
      return currentUser.role;
    }
  }

  public getUseUid(): string {
    const currentUser: any = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
      return currentUser.uuid;
    }
  }

  public getTenantConfig(): string {
    const currentUser: any = JSON.parse(sessionStorage.getItem('tenantConfig'));
    if (currentUser) {
      return currentUser.uuid;
    }
  }
  public getTenantUid(): string {
    const userDetails: any = JSON.parse(sessionStorage.getItem('currentUser'));
    if (userDetails) {
      return userDetails?.tenantUid;
    }
  }

  public getACLList(): any {
    const acl_list: any = JSON.parse(sessionStorage.getItem('acl_list'));
    if (acl_list) {
      return acl_list;
    } else {
      return {}
    }
  }

  public getRouteParam(router: ActivatedRoute, name): string {
    return router.snapshot.params[name]
  }


  public sanitizeImage(response) {
    let objectURL = URL.createObjectURL(response);
    return this._sanitizer.bypassSecurityTrustUrl(objectURL);
  }

  public allowedUploadFileTypes(type): boolean {
    if (type == 'image/jpeg' || type == 'image/png' || type == 'image/jpg' || type == 'application/pdf') {
      return true;
    } else {
      return false;
    }
  }
  public isFileAllowed(fileName: string) {
    let isFileAllowed = false;
    const allowedFiles = ['.pdf', '.jpg', '.jpeg', '.png'];
    const regex = /(?:\.([^.]+))?$/;
    const extension = regex.exec(fileName);
    if (undefined !== extension && null !== extension) {
      for (const ext of allowedFiles) {
        if (ext === extension[0]) {
          isFileAllowed = true;
        }
      }
    }
    return isFileAllowed;
  }
  messageNotification: BehaviorSubject<any> = new BehaviorSubject(false);
  public warning(warning: any) {
    this.messageNotification.next([{ severity: 'warn', summary: 'Warning', detail: warning, }]);
  }
  public info(info: any) {
    this.messageNotification.next([{ severity: 'info', summary: 'Info', detail: info, }]);
  }
  public error(error: any) {
    this.messageNotification.next([{ severity: 'error', summary: 'Error', detail: error, }]);
  }
  public success(success: any) {
    this.messageNotification.next([{ severity: 'success', summary: 'Success', detail: success, }]);
  }


  public handleError(error): void {
    let message = 'Something went wrong. Please try again.';
    if (error && error.message) {
      this.error(error.message);
    }
    else if (error) {
      this.error(error);
    }
    else {
      this.error(message);
    }
  }

  public navigate(path) {
    this._router.navigate([`${path}`]);
  }

  public dateToMilliseconds(date): number {
    return moment(date).valueOf();
  }
  public sortByDateLatest(array: any[], keyA, keyB): any[] {
    return array.sort(function (a, b) {
      var c = new Date(a[keyA]);
      var d = new Date(b[keyB]);
      return Number(d) - Number(c);
    });
  }

  public sortByProperty(array: any[], key): any[] {
    return array.sort((a, b) => (a[key] > b[key]) ? 1 : ((b[key] > a[key]) ? -1 : 0))
  }

  public sortByAlphanumeric(list: any[], key): any[] {
    return list.sort((a, b) => {
      return a[key]?.localeCompare(b[key], undefined, { numeric: true });
    })

  }
  public dateFormat(date: any): any {
    let validData = moment(new Date(date));
    if (validData.isValid()) {
      return moment(new Date(date)).format('MMM Do YYYY');
    } else {
      return "";
    }

  }
  public dateTimeFormat(date: any): any {
    let validData = moment(new Date(date));
    if (validData.isValid()) {
      return moment(new Date(date)).format('MMM Do YYYY hh:mm A');
    } else {
      return "";
    }
  }


  public fileTypeAndIcon(file): string {
    if (file) {
      let type = file?.split('.').pop();
      let icon: string;
      switch (type) {
        case 'pdf':
          icon = 'pdf';
          break;
        case 'jpg':
        case 'jpeg':
        case 'png':
          icon = 'image';
          break;
        default:
          icon = 'file';
          break;
      }
      return icon;
    } else {
      return 'file';
    }
  }

  public historyBack(): void {
    this._location.back();
  }


  public dataURLtoFile(dataurl, filename) {
    var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }

  public daysCalculation(from, to): any {
    const diffInMs = (+new Date(to)) - (+new Date(from))
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24);
    return Math.round(Number(diffInDays)) + 1;
  }



  public isFileAllowedDynamically(fileName: string, allowedTypes: any) {
    let isFileAllowed = false;
    if (allowedTypes == '*') {
      isFileAllowed = true;
    } else {
      const fliesList: any[] = allowedTypes.split(",");
      const allowedFiles: any = fliesList.map(el => el.trim().toLowerCase());
      const regex = /(?:\.([^.]+))?$/;
      const extension = regex.exec(fileName.toLowerCase());
      if (Array.isArray(allowedFiles)) {
        if (allowedFiles.length == 0) {
          if (extension.some(allow => allowedFiles.includes(allow))) {
            isFileAllowed = true;
          } else {
            isFileAllowed = false;
          }
        } else {
          if (extension.some(allow => allowedFiles.includes(allow))) {
            isFileAllowed = true;
          } else {
            isFileAllowed = false;
          }
        }
      } else {
      }
    }
    return isFileAllowed;
  }

  public getHoursFromMinutes(minutes: number): string {
    let convertedTime = ""
    if (minutes > 0) {
      let hours1 = Math.floor(minutes / 60);
      let minutes1 = minutes % 60;
      if (hours1 > 0) {
        convertedTime += (hours1.toString() + " hrs  ")
      }

      if (minutes1 > 0) {
        convertedTime += (minutes1.toString() + " mins")
      }

    }
    return convertedTime
  }



  a = ['', 'one ', 'two ', 'three ', 'four ', 'five ', 'six ', 'seven ', 'eight ', 'nine ', 'ten ', 'eleven ', 'twelve ', 'thirteen ', 'fourteen ', 'fifteen ', 'sixteen ', 'seventeen ', 'eighteen ', 'nineteen '];
  b = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];

  public numberToWords(num) {
    if ((num = num.toString()).length > 9) return 'overflow';
    let n: any = ('000000000' + num).substr(-9).match(/^(\d{2})(\d{2})(\d{2})(\d{1})(\d{2})$/);
    if (!n) return; var str = '';
    str += (n[1] != 0) ? (this.a[Number(n[1])] || this.b[n[1][0]] + ' ' + this.a[n[1][1]]) + 'crore ' : '';
    str += (n[2] != 0) ? (this.a[Number(n[2])] || this.b[n[2][0]] + ' ' + this.a[n[2][1]]) + 'lakh ' : '';
    str += (n[3] != 0) ? (this.a[Number(n[3])] || this.b[n[3][0]] + ' ' + this.a[n[3][1]]) + 'thousand ' : '';
    str += (n[4] != 0) ? (this.a[Number(n[4])] || this.b[n[4][0]] + ' ' + this.a[n[4][1]]) + 'hundred ' : '';
    str += (n[5] != 0) ? ((str != '') ? 'and ' : '') + (this.a[Number(n[5])] || this.b[n[5][0]] + ' ' + this.a[n[5][1]]) + 'only ' : '';
    return str;
  }

  filterData: any = {};
  public setFilterStore(data: any, key: string) {
    this.filterData[key] = data;
  }
  public getFilterStore(key: string) {
    return this.filterData[key];
  }

  public getIndexByLabel(array: any[], label: string): any {
    let index = array.findIndex(item => item.label == label);
    return index;
  }

  public getMonthNameByMonthNumber(month: any): string {
    let monthName: string = '';
    switch (month) {
      case 1:
      case "01":
        monthName = "January";
        break;
      case 2:
      case "02":
        monthName = "February";
        break;
      case 3:
      case "03":
        monthName = "March";
        break;
      case 4:
      case "04":
        monthName = "April";
        break;
      case 5:
      case "05":
        monthName = "May";
        break;
      case 6:
      case "06":
        monthName = "June";
        break;
      case 7:
      case "07":
        monthName = "July";
        break;
      case 8:
      case "08":
        monthName = "August";
        break;
      case 9:
      case "09":
        monthName = "September";
        break;
      case 10:
        monthName = "October";
        break;
      case 11:
        monthName = "November";
        break;
      case 12:
        monthName = "December";
        break;
    }
    return monthName;
  }

  public confirm(message:string,extraString?:string): any {
    const ref: DynamicDialogRef = this._dialogService.open(TpsConfirmationDialogComponent, {
      header: "Confirmation!",
      data: {message:message, extraString:extraString},
      modal: true,
      width: "auto",
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: false,
      maximizable: false,
      // Add these properties to prevent focus issues
      focusOnShow: false
    });
    return ref.onClose;
  }

  messageDialogRef: DynamicDialogRef;
  public showMessage(messageConfig: {
    title: string;
    message: string;
    messageType: string;
  }): any {
    // Close any existing dialog
    if (this._dialogService.dialogComponentRefMap.size > 0) {
      this._dialogService.dialogComponentRefMap.forEach(dialog => {
        dialog.destroy();
      });
    }

    this.messageDialogRef = this._dialogService.open(TpsMessageDialogComponent, {
      header: messageConfig.title,
      data: messageConfig,
      modal: true,
      width: "auto",
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: false,
      maximizable: false,
      // Add these properties to prevent focus issues
      focusOnShow: false,
    });
  }

  public closeMessage(): void {
    this.messageDialogRef.destroy();
  }

  public convertBase64ToBlob(b64Data): any {
    // convert base64 to raw binary data held in a string
    // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
    var byteString = atob(b64Data.split(',')[1]);

    // separate out the mime component
    var mimeString = b64Data.split(',')[0].split(':')[1].split(';')[0]

    // write the bytes of the string to an ArrayBuffer
    var ab = new ArrayBuffer(byteString.length);
    var ia = new Uint8Array(ab);
    for (var i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    // write the ArrayBuffer to a blob, and you're done
    var bb = new Blob([ab], { type: "image/png" });
    return bb;
  }
  // Function to convert Base64 to Blob and upload
  public convertBase64ToPng(base64String, name): any {
    // Convert the Base64 string to binary data
    const byteCharacters = atob(base64String.split(',')[1]); // Remove the "data:image/png;base64," part if it's included
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset++) {
      const byte = byteCharacters.charCodeAt(offset);
      byteArrays.push(byte);
    }

    // Create a Blob from the byte array
    const blob = new Blob([new Uint8Array(byteArrays)], { type: 'image/png' });

    // Create a File object from the Blob
    const file = new File([blob], `${name}.png`, { type: 'image/png' });

    return file;
  }

  public saveToFileSystem(filename, response) {
    //const filename = "orderitems.csv";//parts[1].split('=')[1];
    const blob = new Blob([response], { type: 'text/plain' });
    saveAs(blob, filename);
  }

  public checkNumberOrNot(value, isPercentage?): any {
    if (isPercentage && value >= 101) {
      return 100;
    }
    if (isFinite(value)) {
      return value;
    }
    else if (isNaN(value)) {
      return 0;
    }
    else {
      return 0;
    }
  }


  public prepareTag(label: string, severity?: string): string {
    switch (severity) {
      case "success":
        severity = `<span class="p-tag p-tag-success">${label}</span>`;
        break;
      case "danger":
        severity = `<span class="p-tag p-tag-danger">${label}</span>`;
        break;
      case "warning":
        severity = `<span class="p-tag p-tag-warning">${label}</span>`;
        break;
      case "info":
        severity = `<span class="p-tag p-tag-info">${label}</span>`;
        break;
      case "primary":
        severity = `<span class="p-tag">${label}</span>`;
        break;
      case "secondary":
        severity = `<span class="p-tag p-tag-secondary">${label}</span>`;
        break;
      default:
        severity = `<span class="p-tag">${label}</span>`;
        break;
    }
    return severity;
  }


  public isMobile(): Observable<any> {
    // Monitor screen size changes
    return this.breakpointObserver.observe([
      Breakpoints.XSmall,
      Breakpoints.Small
    ]).pipe(
      takeUntil(this.$destroyed)
    )
  }


  public viewImage(base64: any): void {
    const ref: DynamicDialogRef = this._dialogService.open(TpsViewImageComponent, {
      data: base64,
      header: 'View Image',
      modal: true,
      width: 'auto',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
    });




  }

}
