import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';
import { ProductTypeComponent } from './product-type.component';



export default [
    {
        path: '',
        component: ProductTypeComponent,
        canActivate: [permissionGuard],
        data: { permission: "PRODUCT_TYPE_MASTER" },
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;