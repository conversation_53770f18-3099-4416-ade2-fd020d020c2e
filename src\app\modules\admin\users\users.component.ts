import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { iUser } from 'app/core/models/user.model';
import { SharedModule } from 'app/shared/shared.module';
import {
    actionType,
    buttonActions,
    CommonService,
    FORM_CONTROL_TYPES,
    HTTP_STATUS,
    ICON_BUTTON_SEVERITY,
    ICON_PRIMENG_LIST,
    InvokeService,
    TABLE_ACTION_TYPES,
    toolbarActions,
} from 'app/shared/tapas-ui';
import { ButtonModule } from 'primeng/button';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { TagModule } from 'primeng/tag';
import { BehaviorSubject, finalize, ReplaySubject, takeUntil } from 'rxjs';

import { CreateEditUserComponent } from './create-edit-user/create-edit-user.component';

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [SharedModule, ButtonModule, TagModule],
  templateUrl: './users.component.html'
})
export class UsersComponent implements OnInit, OnDestroy {
  hdr: string = 'Users';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  userList: iUser[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];
  isMobile: boolean = false;

  filterData: any[] = [];
  mainFilterModel: any = {};
  mainFilterModel$: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  selectedRole: any = this._commonService.getUseRole();
  constructor(
    private _commonService: CommonService,
    public _dialogService: DialogService,
    private _invokeService: InvokeService,

  ) { }

  public ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });
    this.prepareTableActions();
    this.prepareMainFilterModel();
    this.getUserRoles();
    this.onRefresh();
  }


  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create User',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.USER_MASTER_CREATE,
        command: (action) => {
          this.onOpenUserDialog('Create User', '', TABLE_ACTION_TYPES.CREATE)
        }
      }
    ];
    this.toolBarActions = [];
  }

  private prepareMainFilterModel(): void {
    this.mainFilterModel = {
      role: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        value: this._commonService.getUseUid(),
        options: [],
        label: "Role",
        placeholder: "Role",
        show: true,
        rules: {
          required: true
        },
      }
    }
  }

  private getUserRoles(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.roles.getUserRoleList)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.prepareUserRoles(this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }

  private prepareUserRoles(data: any[]): void {
    let userRoles: any[] = data.map(item => ({ label: item.code, value: item.code, raw: item }));
    this.mainFilterModel.role.options = userRoles;
    this.mainFilterModel$.next(this.mainFilterModel)
  }

  //on Change Filter
  public onChangeFilter(event): void {
    if (event.role) {
      this.selectedRole = event.role;
      this.onRefresh();
    } else {
      this._commonService.error("Please select role");
    }
  }

  public onRefresh(): void {
    this.getUsers();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [{
      headerName: "Created On", field: "createdTimeText", sortable: true, maxWidth: 180,
    },
    {
      headerName: "Name", field: "fullName", sortable: true, minWidth: 150
    },
    {
      headerName: "User Name", field: "userName", sortable: true, minWidth: 150
    },
    {
      headerName: "Role", field: "role", sortable: true, minWidth: 150
    },
    {
      headerName: "Email", field: "email", sortable: true, minWidth: 180
    },
    {
      headerName: "Contact No", field: "contactNo", sortable: true, minWidth: 150
    },

    {
      headerName: "City", field: "city", sortable: true, minWidth: 150
    },
    {
      headerName: "Country", field: "country", sortable: true, minWidth: 150
    },
    {
      headerName: "Status",
      field: "statusField",
      isTemplate: true,
      sortable: true,
      minWidth: 100
    },


    {
      headerName: 'Actions',
      pinned: 'right',
      maxWidth: 100,
      sortable: false,
      isActionCol: true,
      actions: (params) => this.prepareTableActionIcons(params)
    }
    ];
  }


  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    //assign proper icons for each action
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenUserDialog('View User Details', row, TABLE_ACTION_TYPES.VIEW)
    });
    if (row.uuid != this._commonService.getLoggedInUser().uuid) {
      if (this.ACL_LIST?.USER_MASTER_UPDATE) {
        iconsList.push({
          type: TABLE_ACTION_TYPES.EDIT,
          icon: ICON_PRIMENG_LIST.PI_PENCIL,
          title: "Edit",
          data: row,
          severity: ICON_BUTTON_SEVERITY.INFO,
          onAction: (rowItem) => this.onOpenUserDialog('Edit User Details', row, TABLE_ACTION_TYPES.EDIT)
        });
      }
      if (this.ACL_LIST?.USER_MASTER_DELETE && row.deleted == 0) {
        iconsList.push({
          type: TABLE_ACTION_TYPES.DEACTIVATE,
          icon: ICON_PRIMENG_LIST.PI_POWER_OFF,
          title: "De-Activate", data: row,
          severity: ICON_BUTTON_SEVERITY.DANGER,
          onAction: (rowItem) => this.onDeActivate(row)
        });
      }
      if (this.ACL_LIST?.USER_MASTER_DELETE && row.deleted == 1) {
        iconsList.push({
          type: TABLE_ACTION_TYPES.ACTIVATE,
          icon: ICON_PRIMENG_LIST.PI_POWER_OFF,
          title: "Activate",
          data: row,
          severity: ICON_BUTTON_SEVERITY.SUCCESS,
          onAction: (rowItem) => this.onActivate(row)
        });
      }
    }

    return iconsList;
  }

  private getUsers(): void {
    this._commonService.loadingTableData.next(true);
    APP_UI_CONFIG.administration.users.get.paramList.role = this.selectedRole;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.users.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500); // Optional delay to show skeleton
        })
      )
      .subscribe({
        next: response => {
          let userList = this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime');
          this.prepareUserData(userList)
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }

  private prepareUserData(response: any[]) {
    this.userList = response.map(item => ({
      fullName: item.firstName ? item.lastName ? `${item.firstName} ${item.lastName}` : item.firstName : '',
      enableMultiFactorAuthenticationText: item.mfaEnable ? 'Yes' : 'No',
      createdTimeText: this._commonService.dateTimeFormat(item.createdTime),
      statusField: item.status === 0
        ? this._commonService.prepareTag('ACTIVE', 'success')
        : this._commonService.prepareTag('INACTIVE', 'danger'),
      ...item
    }))
  }

  private onActivate(row): void {
    this._commonService.confirm(`Are you sure you want to activate the user`, row.userName).pipe(takeUntil(this.$destroyed))
      .subscribe(
        (confirm) => {
          if (confirm) {
            APP_UI_CONFIG.administration.users.activateUser.paramList.uuid = row.uuid; //API integration pending
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.users.activateUser).pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response && response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`${row.userName} user activated successfully`);
                    this.onRefresh();
                  } else {
                    this._commonService.handleError(response);
                  }
                },
                error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        },
      )
  }
  private onDeActivate(row): void {
    this._commonService.confirm(`Are you sure you want to de-activate the user`, row.userName).pipe(
      takeUntil(this.$destroyed))
      .subscribe(
        (confirm) => {
          if (confirm) {
            APP_UI_CONFIG.administration.users.deActivateUser.paramList.uuid = row.uuid; //API integration pending
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.users.deActivateUser).pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response && response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`${row.userName} user de-activated successfully`);
                    this.onRefresh();
                  } else {
                    this._commonService.handleError(response);
                  }
                },
                error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        },
      )
  }



  private onOpenUserDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditUserComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: this.isMobile ? '100%' : '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': this.isMobile ? '0' : '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }




  //destroy
  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
