import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Technician } from 'app/core/models/technician.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  actionType,
  buttonActions,
  CommonService,
  ICON_BUTTON_SEVERITY,
  ICON_PRIMENG_LIST,
  InvokeService,
  TABLE_ACTION_TYPES,
  toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';
import { CreateEditTechnicianComponent } from './create-edit-technician/create-edit-technician.component';




@Component({
  selector: 'tps-technician-list',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './technician-list.component.html'
})
export class TechnicianListComponent implements OnInit, On<PERSON><PERSON>roy {
  hdr: string = 'Technicians';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  technicians: Technician[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Technician',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.TECHNICIAN_CREATE,
        command: () => {
          this.onOpenTechnicianDialog('Create Technician', new Technician(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getTechnicians();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      {
        headerName: "First Name",
        field: "firstName",
        sortable: true,
      },
      {
        headerName: "Last Name",
        field: "lastName",
        sortable: true,
      },

      {
        headerName: "Email",
        field: "email",
        sortable: true,
      },
      {
        headerName: "Contact No",
        field: "contactNo",
        sortable: true,
      },
      {
        headerName: "City",
        field: "city",
        sortable: true,
      },
      {
        headerName: "Created Date",
        field: "createdDateText",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenTechnicianDialog('View Technician Details', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.TECHNICIAN_UPDATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenTechnicianDialog('Edit Technician Details', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (this.ACL_LIST?.TECHNICIAN_DELETE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DELETE,
        icon: ICON_PRIMENG_LIST.PI_TRASH,
        title: "Delete",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.deleteTechnician(row)
      });
    }

    return iconsList;
  }

  private getTechnicians(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.technician.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.prepareTechnicianData(this._commonService.sortByDateLatest(response, 'createdDate', 'createdDate'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareTechnicianData(data: any[]): void {
    this.technicians = data.map(item => ({
      createdDateText: this._commonService.dateTimeFormat(item.createdDate),
      ...item
    }));

  }

  private deleteTechnician(row: Technician): void {
    this._commonService.confirm('Are you sure you want to delete this technician?', row.fullName)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.technician.delete, row.uuid)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Technician deleted successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  private onOpenTechnicianDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditTechnicianComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
