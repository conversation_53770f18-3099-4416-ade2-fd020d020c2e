import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { ProductType } from 'app/core/models/product-type.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  actionType,
  buttonActions,
  CommonService,
  ICON_BUTTON_SEVERITY,
  ICON_PRIMENG_LIST,
  InvokeService,
  TABLE_ACTION_TYPES,
  toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';
import { CreateEditProductTypeComponent } from './create-edit-product-type/create-edit-product-type.component';

@Component({
  selector: 'tps-product-type',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './product-type.component.html',
})
export class ProductTypeComponent implements OnInit, OnD<PERSON>roy {
  hdr: string = 'Product Types';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  productTypes: ProductType[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];
  isMobile: boolean = false;

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Product Type',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.PRODUCT_TYPE_MASTER_CREATE,
        command: () => {
          this.onOpenProductTypeDialog('Create Product Type', new ProductType(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getProductTypes();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      {
        headerName: "Name",
        field: "name",
        sortable: true,
      },
      {
        headerName: "Description",
        field: "description",
        sortable: true,
      },
      {
        headerName: "Created Date",
        field: "createdDateText",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenProductTypeDialog('View Product Type', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.PRODUCT_TYPE_MASTER_EDIT) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenProductTypeDialog('Edit Product Type', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (this.ACL_LIST?.PRODUCT_TYPE_MASTER_DELETE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DELETE,
        icon: ICON_PRIMENG_LIST.PI_TRASH,
        title: "Delete",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.deleteProductType(row)
      });
    }

    return iconsList;
  }

  private getProductTypes(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.productType.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.prepareProductTypeData(this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareProductTypeData(data: any[]): void {
    this.productTypes = data.map(item => ({
      createdDateText: this._commonService.dateTimeFormat(item.createdTime),
      ...item
    }));
  }

  private deleteProductType(row: ProductType): void {
    this._commonService.confirm('Are you sure you want to delete this product type?', row.product)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          APP_UI_CONFIG.administration.productType.delete.paramList.uuid = row.uuid;
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.productType.delete)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Product type deleted successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  private onOpenProductTypeDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditProductTypeComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: this.isMobile ? '100%' : '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': this.isMobile ? '0' : '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}