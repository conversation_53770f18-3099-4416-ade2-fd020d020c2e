import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { Auditor } from 'app/core/models/auditor.model';
import { UserRole } from 'app/core/models/user-role.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReplaySubject, takeUntil } from 'rxjs';

import { AUDITOR_FORM_MODEL } from './auditor-form.model';

@Component({
  selector: 'tps-create-edit-auditor',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-auditor.component.html',
  styleUrl: './create-edit-auditor.component.scss'
})
export class CreateEditAuditorComponent implements OnInit, OnDestroy {
  public hdr: string = 'Create Auditor';
  public auditorForm: FormGroup;
  public fields: any[] = [];
  public rec: Auditor = new Auditor();
  isMobile: boolean = false;
  userRoles: UserRole[] = [];
  mappingOptions: any[] = [];

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;

  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;
    this.buildForm();
    this.getUserRoles();
    this.getMappingOptions();
    this.configureForm();
  }

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(AUDITOR_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(AUDITOR_FORM_MODEL);
    this.auditorForm = new FormGroup(formGroupFields);
  }

  private getUserRoles(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.roles.getUserRoleList)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.userRoles = response;
          this.prepareUserRoles();
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareUserRoles(): void {
    const roleField = this.fields.find(field => field.name === 'role');
    if (roleField) {
      this.userRoles=[{roleName:'Auditor',roleType:'AUDITOR'}]
      roleField.options = this.userRoles.map(role => {
        return {
          label: role.roleName,
          value: role.roleType
        };
      });
    }
  }

  private getMappingOptions(): void {
    // This would be replaced with your actual API call to get mapping options
    // this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.getMappingOptions)
    //   .pipe(takeUntil(this.$destroyed))
    //   .subscribe({
    //     next: response => {
    //       this.mappingOptions = response;
    //       this.prepareMappingOptions();
    //     },
    //     error: error => {
    //       this._commonService.handleError(error);
    //     }
    //   });
  }

  private prepareMappingOptions(): void {
    const mappingField = this.fields.find(field => field.name === 'mappedWith');
    if (mappingField) {
      mappingField.options = this.mappingOptions.map(option => {
        return {
          label: option.name,
          value: option.id
        };
      });
    }
  }

  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Auditor";
        this.setDataToForm();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View Auditor";
        this.setDataToForm();
        this.auditorForm.disable();
        break;
      default:
        break;
    }
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    this.auditorForm = this._formFactoryService.setFormControlsValues(this.auditorForm, this.rec, this.fields);
  }

  //submit form
  public onSubmit() {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.auditorForm, this.rec, this.fields);
    this.rec.fullName = this.rec.firstName ? this.rec.lastName ? `${this.rec.firstName} ${this.rec.lastName}` : this.rec.firstName : '';

    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update this auditor?`, this.rec.fullName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.administration.auditor.update.paramList.uuid = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Auditor updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the auditor details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    } else {
      this._commonService.confirm(`Are you sure you want to create a new auditor?`, this.rec.fullName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New auditor created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the auditor details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}