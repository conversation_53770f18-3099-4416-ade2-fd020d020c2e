import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';

import { UsersComponent } from './users/users.component';

export default [
    {
        path: 'users',
        component: UsersComponent,
        canActivate: [permissionGuard],
        data: { permission: "USERS_MASTER" },
    },
    {
        path: 'role',
        canActivate: [permissionGuard],
        data: { permission: "ROLE_MASTER" },
        loadChildren: () => import('./role/role.routes')
    },
    {
        path: 'brand',
        canActivate: [permissionGuard],
        data: { permission: "BRAND_MASTER" },
        loadChildren: () => import('./brand/brand.routes')
    },
    {
        path: 'category',
        canActivate: [permissionGuard],
        data: { permission: "CATEGORY_MASTER" },
        loadChildren: () => import('./category/category.routes')
    },
    {
        path: 'auditor',
        canActivate: [permissionGuard],
        data: { permission: "AUDITOR_MASTER" },
        loadChildren: () => import('./auditor/auditor.routes')
    },
    {
        path: 'defectType',
        canActivate: [permissionGuard],
        data: { permission: "DEFECT_TYPE_MASTER" },
        loadChildren: () => import('./defect-type/defect-type.routes')
    },
    {
        path: 'productType',
        canActivate: [permissionGuard],
        data: { permission: "PRODUCT_TYPE_MASTER" },
        loadChildren: () => import('./product-type/product-type.routes')
    },
    {
        path: 'factory',
        canActivate: [permissionGuard],
        data: { permission: "FACTORY_MASTER" },
        loadChildren: () => import('./factory/factory.routes')
    },
     {
        path: 'technician',
        canActivate: [permissionGuard],
        data: { permission: "ADMIN_TECHNICIAN_MASTER" },
        loadChildren: () => import('./technician/technician.routes')
    },
    {
        path: 'vendor',
        canActivate: [permissionGuard],
        data: { permission: "VENDOR_MASTER" },
        loadChildren: () => import('./vendor/vendor.routes')
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;
