import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const PRODUCT_TYPE_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. PRD001',
        topGroupTitleIcon: 'pi-box',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Product Type Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    name: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Ex. Product Name',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    description: {
        type: FORM_CONTROL_TYPES.TEXTAREA,
        value: "",
        label: "Description",
        placeholder: 'Ex. Product Description',
        show: true,
        rules: {
            maxLength: 500,
        }
    },
}